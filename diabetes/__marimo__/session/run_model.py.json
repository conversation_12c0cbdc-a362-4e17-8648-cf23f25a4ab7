{"version": "1", "metadata": {"marimo_version": "0.13.11"}, "cells": [{"id": "Hbol", "code_hash": "1d0db38904205bec4d6f6f6a1f6cec3e", "outputs": [{"type": "data", "data": {"text/plain": ""}}], "console": []}, {"id": "MJUe", "code_hash": "191b8df5c96061ddbb0b268d81cab20c", "outputs": [], "console": []}, {"id": "vblA", "code_hash": "01ffc6a5ff0b5ac741f720d6422c00b9", "outputs": [], "console": []}, {"id": "bkHC", "code_hash": "9b8dccde98660a0868aa658e13fb6b4e", "outputs": [], "console": []}, {"id": "lEQa", "code_hash": "123a10b242235bf5e85195b56d25b8a6", "outputs": [], "console": []}, {"id": "P<PERSON><PERSON>", "code_hash": "63f32ca5d5011687f2940d1e8859cf39", "outputs": [], "console": []}, {"id": "<PERSON><PERSON><PERSON>", "code_hash": "e50c147f542c2768b5f128aa28b7d68d", "outputs": [], "console": []}, {"id": "SFPL", "code_hash": "d9f5cacb2da6b7e3651f466f0f6043f8", "outputs": [], "console": []}, {"id": "BYtC", "code_hash": "66aa0c4771de6996ccc9352cf69b0cce", "outputs": [{"type": "data", "data": {"text/html": "<span class=\"markdown prose dark:prose-invert\"><h2 id=\"the-model-with-the-nearest-firing-rates\">The model with the nearest firing rates</h2>\n<span class=\"paragraph\">But the neurons are very similar to each other and the CV of force is musch bigger than the experimental one (between 1.5% and 2%).</span></span>"}}], "console": []}, {"id": "RGSE", "code_hash": "d06b86d5e919c80a50e785b54078d368", "outputs": [], "console": []}, {"id": "Kclp", "code_hash": "f67e1020820bba0da601c1ee6fcc6ef8", "outputs": [], "console": []}, {"id": "emfo", "code_hash": "9c98e018a3da0ded60831c0d526af8b6", "outputs": [], "console": []}, {"id": "Hstk", "code_hash": null, "outputs": [], "console": []}]}
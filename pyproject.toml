[project]
name = "modelo-motoneuronio"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "impi-rt>=2021.15.0 ; sys_platform == 'win32'",
    "jupyter>=1.1.1",
    "libneuroml>=0.6.5",
    "marimo>=0.13.15",
    "matplotlib>=3.10.1",
    "mpi4py>=4.0.3",
    "neuron>=8.2.6 ; sys_platform == 'linux'",
    "numpy==1.26",
    "pandas>=2.2.3",
    "pyneuroml>=1.3.15",
    "pynn>=0.12.4",
    "pyqt6>=6.9.0",
    "python-lsp-server>=1.12.2",
    "ruff>=0.11.9",
    "scipy>=1.15.3",
    "websockets>=15.0.1",
]

[tool.marimo.language_servers.pylsp]
enabled = true               # Enable/disable the Python language server
enable_ruff = true           # Linting with ruff (enabled by default, if installed)

# Diagnostics configuration
[tool.marimo.diagnostics]
enabled = true               # Show diagnostics in the editor

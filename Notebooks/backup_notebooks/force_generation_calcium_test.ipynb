{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.016Z", "start_time": "2025-05-10T07:14:43.591571Z"}}, "source": ["import os\n", "import shutil\n", "import sys\n", "import platform\n", "sys.path.append('./../')\n", "\n", "from pyNN.utility.build import compile_nmodl\n", "\n", "if platform.system() == 'Linux':\n", "    shutil.copyfile('./../src/mn.mod','./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/mn.mod')\n", "    shutil.copyfile('./../src/muscle_unit.mod','./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/muscle_unit.mod')\n", "    shutil.copyfile('./../src/muscle_unit_calcium.mod','./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/muscle_unit_calcium.mod')\n", "    shutil.copyfile('./../src/gammapointprocess.mod','./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/gammapointprocess.mod')\n", "    compile_nmodl('./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/')\n", "    # h.nrn_load_dll('./../modelpynn/lib/python3.10/site-packages/pyNN/neuron/nmodl/mn.o')\n", "if platform.system() == 'Windows':\n", "    shutil.copyfile('../src/mn.mod','../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/mn.mod')\n", "    shutil.copyfile('./../src/muscle_unit.mod','./../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/muscle_unit.mod')\n", "    shutil.copyfile('./../src/muscle_unit_calcium.mod','./../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/muscle_unit_calcium.mod')\n", "    shutil.copyfile('../src/gammapointprocess.mod','../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/gammapointprocess.mod')\n", "    compile_nmodl('../modelpynn/Lib/site-packages/pyNN/neuron/nmodl')\n", "    h.nrn_load_dll('modelpynn/Lib/site-packages/pyNN/neuron/nmodl/mn.o')\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pyNN\n", "import pyNN.neuron as sim\n", "import pyNN.space as space\n", "\n", "\n", "from neuroml import Morphology, Segment, Point3DWithDiam as P\n", "from neuron import h, hclass\n", "from pyNN import neuron\n", "from pyNN.models import BaseCellType\n", "from pyNN.morphology import NeuroMLMorphology, NeuriteDistribution, Morphology as Morph, IonChannelDistribution\n", "from pyNN.neuron import NativeCellType\n", "from pyNN.neuron.cells import RandomSpikeSource, _new_property\n", "from pyNN.neuron.morphology import uniform, random_section, random_placement, at_distances, apical_dendrites, dendrites, centre\n", "from pyNN.neuron.simulator import state\n", "from pyNN.parameters import IonicSpecies\n", "from pyNN.random import RandomDistribution, NumpyRNG\n", "from pyNN.space import Grid2D, RandomStructure, Sphere\n", "from pyNN.standardmodels import StandardIonChannelModel, build_translations, StandardCellType, StandardModelType\n", "from pyNN.standardmodels.cells import SpikeSourceGamma, MultiCompartmentNeuron as mc\n", "\n", "from pyNN.utility.plotting import Figure, Panel\n", "import src.Classes as Classes\n", "import src.functions as funçoes\n", "from src.functions import neuromuscular_system, soma_força\n", "# %matplotlib widget"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nrnivmodl found at /home/<USER>/code/Modelo-Motoneuronio/.venv/bin/nrnivmodl\n", "Successfully compiled NEURON extensions.\n", "numprocs=1\n"]}], "execution_count": 2}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.031386Z", "start_time": "2025-05-10T07:14:45.028253Z"}}, "source": ["timestep = 0.05\n", "sim.setup(timestep=timestep)\n", "Tf = 1000\n", "n = 100\n", "somas = funçoes.create_somas(n)\n", "dends = funçoes.create_dends(n,somas)"], "outputs": [], "execution_count": 3}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.080061Z", "start_time": "2025-05-10T07:14:45.074563Z"}}, "source": ["cell_type = Classes.cell_class(\n", "    morphology= funçoes.soma_dend(somas, dends) ,\n", "    cm=1,    # mF / cm**2\n", "    Ra=0.070, # ohm.mm\n", "    ionic_species={\"na\": IonicSpecies(\"na\", reversal_potential=50),\n", "                   \"ks\": IonicSpecies(\"ks\", reversal_potential=-80),\n", "                   \"kf\": IonicSpecies(\"kf\", reversal_potential=-80)\n", "                  },\n", "    pas_soma = {\"conductance_density\": uniform('soma', 7e-4), \"e_rev\":-70},\n", "    pas_dend = {\"conductance_density\": uniform('dendrite', 7e-4), \"e_rev\":-70},\n", "    na = {\"conductance_density\": uniform('soma', 10), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "    kf = {\"conductance_density\": uniform('soma', 1), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "    ks = {\"conductance_density\": uniform('soma', 0.5), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "    syn={\"locations\": centre('dendrite'),\n", "         \"e_syn\": 0,\n", "         \"tau_syn\": 0.6},  \n", ")"], "outputs": [], "execution_count": 4}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.141404Z", "start_time": "2025-05-10T07:14:45.121282Z"}}, "source": ["cells = sim.Population(n, cell_type, initial_values={'v': list(-70*np.ones(n))})\n"], "outputs": [], "execution_count": 5}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.169344Z", "start_time": "2025-05-10T07:14:45.166763Z"}}, "source": "muscle_units, force_objects, neuromuscular_junctions = neuromuscular_system(cells, n, h)", "outputs": [], "execution_count": 6}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.345861Z", "start_time": "2025-05-10T07:14:45.214815Z"}}, "source": ["np.random.seed(26278342)\n", "spike_source = sim.Population(400, Classes.SpikeSourceGammaStart(alpha=1)) \n", "                                                        #start=RandomDistribution('uniform', [0, 3.0], rng=NumpyRNG(seed=4242))))\n", "syn = sim.StaticSynapse(weight=0.6, delay=0.2)\n", "# nmj = sim.StaticSynapse(weight=1, delay=0.2)\n", "input_conns = sim.Projection(spike_source, cells, \n", "                             sim.FixedProbabilityConnector(0.3, location_selector='dendrite'), \n", "                             syn, receptor_type=\"syn\")\n"], "outputs": [], "execution_count": 7}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.362083Z", "start_time": "2025-05-10T07:14:45.353172Z"}}, "source": ["spike_source.record('spikes')\n", "cells.record('spikes')\n", "cells[0:2].record('v', locations=('dendrite','soma'))\n", "cells[0:2].record(('na.m', 'na.h'), locations='soma')\n", "cells[0:2].record(('kf.n'), locations='soma')\n", "cells[0:2].record(('ks.p'), locations='soma')\n", "f = dict()\n", "cat = dict()\n", "for i in range(n):\n", "    f[i] = h.Vector().record(force_objects[i]._ref_F)\n", "    cat[i] = h.Vector().record(force_objects[i]._ref_CaT)\n"], "outputs": [], "execution_count": 8}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.404224Z", "start_time": "2025-05-10T07:14:45.402579Z"}}, "source": ["# soma_força(force_objects, 100)\n", "# f, força_total = soma_força(force_objects,100)"], "outputs": [], "execution_count": 9}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.503540Z", "start_time": "2025-05-10T07:14:45.450495Z"}}, "source": ["sim.run(Tf, callbacks=[Classes.SetRate(spike_source, cells)])"], "outputs": [{"ename": "TypeError", "evalue": "SetRate.__init__() missing 1 required positional argument: 'force_objects'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[10], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m sim\u001b[38;5;241m.\u001b[39mrun(Tf, callbacks\u001b[38;5;241m=\u001b[39m[\u001b[43mClasses\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mSetRate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspike_source\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcells\u001b[49m\u001b[43m)\u001b[49m])\n", "\u001b[0;31mTypeError\u001b[0m: SetRate.__init__() missing 1 required positional argument: 'force_objects'"]}], "execution_count": 10}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:14:45.505939967Z", "start_time": "2025-05-09T18:48:08.539548Z"}}, "source": ["figure_filename = 'teste.png'\n", "data_source = spike_source.get_data().segments\n", "data = cells.get_data().segments[0]\n", "vm = data.filter(name=\"soma.v\")[0]\n", "m = data.filter(name=\"soma.na.m\")[0]\n", "h = data.filter(name=\"soma.na.h\")[0]\n", "n = data.filter(name=\"soma.kf.n\")[0]\n", "p = data.filter(name=\"soma.ks.p\")[0]\n", "# ina = data.filter(name=\"soma.na.ina\")[0]\n", "# ik = data.filter(name=\"ik\")[0]\n", "\n", "# gsyn = data.filter(name=\"gsyn_exc\")[0]\n", "Figure(\n", "    Panel(data_source.spiketrains, xlabel=\"Time (ms)\", xticks=True, yticks=True),\n", "    Panel(vm, ylabel=\"Membrane potential (mV)\", xticks=True, yticks=True),\n", "    Panel(m, ylabel=\"m state\", xticks=True, yticks=True),\n", "    Panel(h, ylabel=\"h state\", xticks=True, yticks=True),\n", "    Panel(n, ylabel=\"n state\", xticks=True, yticks=True),\n", "    Panel(p, ylabel=\"p state\", xticks=True, yticks=True),\n", "    # Panel(ina, ylabel=\"i_na (mA)\", xticks=True, yticks=True),\n", "    # Panel(ik, ylabel=\"i_k (mA)\", xticks=True, yticks=True),\n", "\n", "    # Panel(gsyn, ylabel=\"Synaptic conductance (uS)\"),\n", "    Panel(data.spiketrains, xlabel=\"Time (ms)\", xticks=True, yticks=True),\n", ").save(figure_filename)\n", "\n", "\n", "plt.figure()\n", "plt.plot(np.arange(0, Tf+timestep, timestep), f[0])\n", "plt.show()\n", "\n", "plt.figure()\n", "plt.plot(np.arange(0, Tf+timestep, timestep), cat[0])\n", "plt.show()\n"], "outputs": [{"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mIndexError\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m figure_filename = \u001b[33m'\u001b[39m\u001b[33mteste.png\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m data_source = \u001b[43mspike_source\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m.segments\n\u001b[32m      3\u001b[39m data = cells.get_data().segments[\u001b[32m0\u001b[39m]\n\u001b[32m      4\u001b[39m vm = data.filter(name=\u001b[33m\"\u001b[39m\u001b[33msoma.v\u001b[39m\u001b[33m\"\u001b[39m)[\u001b[32m0\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/code/Modelo-Motoneuronio/.venv/lib/python3.12/site-packages/pyNN/common/populations.py:524\u001b[39m, in \u001b[36mBasePopulation.get_data\u001b[39m\u001b[34m(self, variables, gather, clear, locations)\u001b[39m\n\u001b[32m    508\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget_data\u001b[39m(\u001b[38;5;28mself\u001b[39m, variables=\u001b[33m'\u001b[39m\u001b[33mall\u001b[39m\u001b[33m'\u001b[39m, gather=\u001b[38;5;28;01mTrue\u001b[39;00m, clear=\u001b[38;5;28;01mFalse\u001b[39;00m, locations=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    509\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    510\u001b[39m \u001b[33;03m    Return a Neo `Block` containing the data (spikes, state variables)\u001b[39;00m\n\u001b[32m    511\u001b[39m \u001b[33;03m    recorded from the Population.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    522\u001b[39m \u001b[33;03m    If `clear` is True, recorded data will be deleted from the `Population`.\u001b[39;00m\n\u001b[32m    523\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m524\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrecorder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgather\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_record_filter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclear\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlocations\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlocations\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/code/Modelo-Motoneuronio/.venv/lib/python3.12/site-packages/pyNN/recording/__init__.py:437\u001b[39m, in \u001b[36mRecorder.get\u001b[39m\u001b[34m(self, variables, gather, filter_ids, clear, annotations, locations)\u001b[39m\n\u001b[32m    435\u001b[39m data.name = \u001b[38;5;28mself\u001b[39m.population.label\n\u001b[32m    436\u001b[39m data.description = \u001b[38;5;28mself\u001b[39m.population.describe()\n\u001b[32m--> \u001b[39m\u001b[32m437\u001b[39m data.rec_datetime = \u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43msegments\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m.rec_datetime\n\u001b[32m    438\u001b[39m data.annotate(**\u001b[38;5;28mself\u001b[39m.metadata)\n\u001b[32m    439\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m annotations:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/code/Modelo-Motoneuronio/.venv/lib/python3.12/site-packages/neo/core/objectlist.py:90\u001b[39m, in \u001b[36mObjectList.__getitem__\u001b[39m\u001b[34m(self, i)\u001b[39m\n\u001b[32m     89\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__getitem__\u001b[39m(\u001b[38;5;28mself\u001b[39m, i):\n\u001b[32m---> \u001b[39m\u001b[32m90\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_items\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\n", "\u001b[31mIndexError\u001b[39m: list index out of range"]}], "execution_count": 15}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([  3.2 ,   8.2 ,  12.95,  19.5 ,  22.95,  27.4 ,  31.95,  35.2 ,\n", "        38.75,  41.7 ,  45.1 ,  48.25,  51.25,  54.15,  57.3 ,  60.55,\n", "        63.5 ,  66.85,  69.95,  73.1 ,  76.1 ,  78.95,  81.85,  84.9 ,\n", "        88.1 ,  91.1 ,  94.05,  97.  , 100.05, 103.9 , 107.05, 111.05,\n", "       113.8 , 117.9 , 121.05, 124.5 , 127.9 , 131.8 , 135.95, 139.9 ,\n", "       147.25, 151.4 , 155.4 , 170.9 , 274.05, 283.4 , 287.25, 292.45,\n", "       297.55, 301.1 , 304.85, 309.  , 312.6 , 316.  , 319.65, 322.85,\n", "       326.8 , 330.3 , 333.5 , 337.25, 340.35, 343.45, 346.7 , 349.9 ,\n", "       353.3 , 355.95, 359.15, 362.4 , 366.45, 370.  , 373.85, 378.  ,\n", "       382.1 , 387.35, 390.55, 398.35, 406.35, 530.65, 535.2 , 539.35,\n", "       544.1 , 549.85, 553.4 , 557.3 , 561.65, 565.25, 568.55, 571.95,\n", "       575.25, 578.3 , 581.8 , 584.9 , 588.2 , 591.5 , 595.25, 598.9 ,\n", "       601.75, 604.7 , 607.85, 611.35, 614.8 , 619.75, 624.45, 628.7 ,\n", "       632.7 , 635.7 , 639.9 , 644.2 , 649.05, 653.7 , 661.7 , 674.4 ,\n", "       770.8 , 776.5 , 783.1 , 787.5 , 791.3 , 794.6 , 797.75, 802.2 ,\n", "       807.8 , 811.  , 814.85, 818.4 , 821.3 , 825.3 , 828.55, 831.95,\n", "       834.85, 838.5 , 841.75, 845.15, 848.2 , 851.15, 854.65, 858.1 ,\n", "       861.1 , 864.3 , 868.7 , 872.65, 876.25, 880.85, 884.35, 888.45,\n", "       892.8 , 897.6 , 901.35, 906.05, 914.55, 931.4 , 937.75])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": "data.spiketrains[0].as_array()\n"}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2025-05-09T07:11:08.198996Z", "start_time": "2025-05-09T07:11:07.100890Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n"]}, {"ename": "AttributeError", "evalue": "'hoc.HocObject' object has no attribute 'GammaProcess'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[12]\u001b[39m\u001b[32m, line 31\u001b[39m\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplatform\u001b[39;00m\n\u001b[32m     30\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpyNN\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mneuron\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcells\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m RandomSpikeSource\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mClasses\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mClasses\u001b[39;00m\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfunçoes\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mfunçoes\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/code/Modelo-Motoneuronio/Notebooks/../src/Classes.py:170\u001b[39m\n\u001b[32m    166\u001b[39m         \u001b[38;5;28mself\u001b[39m.population_source.set(beta=rate)\n\u001b[32m    167\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m t + \u001b[38;5;28mself\u001b[39m.interval\n\u001b[32m--> \u001b[39m\u001b[32m170\u001b[39m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mRandomGammaStartSpikeSource\u001b[39;00m(hclass(\u001b[43mh\u001b[49m\u001b[43m.\u001b[49m\u001b[43mGammaProcess\u001b[49m)):\n\u001b[32m    171\u001b[39m     parameter_names = (\u001b[33m\"\u001b[39m\u001b[33malpha\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mbeta\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mduration\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    173\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, alpha=\u001b[32m1\u001b[39m, beta=\u001b[32m0.1\u001b[39m, start=\u001b[32m0\u001b[39m, duration=\u001b[32m0\u001b[39m):\n", "\u001b[31mAttributeError\u001b[39m: 'hoc.HocObject' object has no attribute 'GammaProcess'"]}], "source": ["import sys\n", "sys.path.append('./../')\n", "\n", "from pyNN.random import RandomDistribution, NumpyRNG\n", "from pyNN import neuron\n", "import pyNN.space as space\n", "import pyNN\n", "import pyNN.neuron as sim\n", "import numpy as np\n", "from pyNN.utility.plotting import Figure, Panel\n", "from pyNN.space import Grid2D, RandomStructure, Sphere\n", "import matplotlib.pyplot as plt\n", "from neuroml import Morphology, Segment, Point3DWithDiam as P\n", "from pyNN.morphology import NeuroMLMorphology, NeuriteDistribution, Morphology as Morph, IonChannelDistribution\n", "from pyNN.neuron.morphology import uniform, random_section, random_placement, at_distances, apical_dendrites, dendrites, centre\n", "from pyNN.parameters import IonicSpecies\n", "from pyNN.standardmodels import StandardIonChannelModel, build_translations, StandardCellType\n", "from pyNN.standardmodels.cells import SpikeSourceGamma\n", "from pyNN.neuron import NativeCellType\n", "import shutil\n", "import os\n", "from neuron import h, hclass\n", "\n", "h.nrn_load_dll(\"../src/gammapointprocess.o\")\n", "\n", "from pyNN.utility.build import compile_nmodl\n", "from pyNN.standardmodels.cells import MultiCompartmentNeuron as mc\n", "import platform\n", "from pyNN.neuron.cells import RandomSpikeSource\n", "import src.Classes as Classes\n", "import src.funçoes as funçoes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if platform.system() == 'Linux':\n", "    shutil.copyfile('./../src/mn.mod','./../modelpynn/lib/python3.10/site-packages/pyNN/neuron/nmodl/mn.mod')\n", "    shutil.copyfile('./../src/gammapointprocess.mod','./../modelpynn/lib/python3.10/site-packages/pyNN/neuron/nmodl/gammapointprocess.mod')\n", "    compile_nmodl('./../modelpynn/lib/python3.10/site-packages/pyNN/neuron/nmodl/')\n", "    h.nrn_load_dll('./../modelpynn/lib/python3.10/site-packages/pyNN/neuron/nmodl/mn.o')\n", "if platform.system() == 'Windows':\n", "    shutil.copyfile('../src/mn.mod','../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/mn.mod')\n", "    shutil.copyfile('../src/gammapointprocess.mod','../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/gammapointprocess.mod')\n", "    compile_nmodl('../modelpynn/Lib/site-packages/pyNN/neuron/nmodl')\n", "    h.nrn_load_dll('modelpynn/Lib/site-packages/pyNN/neuron/nmodl/mn.o')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sim.setup(timestep=0.05)\n", "\n", "n = 100\n", "somas = funçoes.create_somas(n)\n", "dends = funçoes.create_dends(n,somas)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cell_type = Classes.cell_class(\n", "    morphology= funçoes.soma_dend(somas, dends) ,\n", "    cm=1,    # mF / cm**2\n", "    Ra=0.070, # ohm.mm\n", "    ionic_species={\"na\": IonicSpecies(\"na\", reversal_potential=50),\n", "                   \"ks\": IonicSpecies(\"ks\", reversal_potential=-80),\n", "                   \"kf\": IonicSpecies(\"kf\", reversal_potential=-80)\n", "                  },\n", "    pas_soma = {\"conductance_density\": uniform('soma', 7e-4), \"e_rev\":-70},\n", "    pas_dend = {\"conductance_density\": uniform('dendrite', 7e-4), \"e_rev\":-70},\n", "    na = {\"conductance_density\": uniform('soma', 10), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "    kf = {\"conductance_density\": uniform('soma', 1), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "    ks = {\"conductance_density\": uniform('soma', 0.5), \"vt\":list(np.linspace(-57.65, -53,100))},\n", "\n", "    syn={\n", "        \"locations\": centre('dendrite'),\n", "        \"e_syn\": 0,\n", "        \"tau_syn\": 0.6},  \n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cells = sim.Population(n, cell_type, initial_values={'v': list(-70*np.ones(n))})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.random.seed(26278342)\n", "spike_source = sim.Population(400, Classes.SpikeSourceGammaStart(alpha=1)) \n", "                                                        #start=RandomDistribution('uniform', [0, 3.0], rng=NumpyRNG(seed=4242))))\n", "syn = sim.StaticSynapse(weight=0.6, delay=0.2)\n", "input_conns = sim.Projection(spike_source, cells, \n", "                             sim.FixedProbabilityConnector(0.3, location_selector='dendrite'), \n", "                             syn, receptor_type=\"syn\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spike_source.record('spikes')\n", "cells.record('spikes')\n", "cells[0:2].record('v', locations=('dendrite','soma'))\n", "cells[0:2].record(('na.m', 'na.h'), locations='soma')\n", "cells[0:2].record(('kf.n'), locations='soma')\n", "cells[0:2].record(('ks.p'), locations='soma')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sim.run(1000, callbacks=[Classes.SetRate(spike_source, cells)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["figure_filename = 'teste.png'\n", "data_source = spike_source.get_data().segments[0]\n", "data = cells.get_data().segments[0]\n", "vm = data.filter(name=\"soma.v\")[0]\n", "m = data.filter(name=\"soma.na.m\")[0]\n", "h = data.filter(name=\"soma.na.h\")[0]\n", "n = data.filter(name=\"soma.kf.n\")[0]\n", "p = data.filter(name=\"soma.ks.p\")[0]\n", "# ina = data.filter(name=\"soma.na.ina\")[0]\n", "# ik = data.filter(name=\"ik\")[0]\n", "\n", "# gsyn = data.filter(name=\"gsyn_exc\")[0]\n", "Figure(\n", "    Panel(data_source.spiketrains, xlabel=\"Time (ms)\", xticks=True, yticks=True),\n", "    Panel(vm, ylabel=\"Membrane potential (mV)\", xticks=True, yticks=True),\n", "    Panel(m, ylabel=\"m state\", xticks=True, yticks=True),\n", "    Panel(h, ylabel=\"h state\", xticks=True, yticks=True),\n", "    Panel(n, ylabel=\"n state\", xticks=True, yticks=True),\n", "    Panel(p, ylabel=\"p state\", xticks=True, yticks=True),\n", "    # Panel(ina, ylabel=\"i_na (mA)\", xticks=True, yticks=True),\n", "    # Panel(ik, ylabel=\"i_k (mA)\", xticks=True, yticks=True),\n", "\n", "    # Panel(gsyn, ylabel=\"Synaptic conductance (uS)\"),\n", "    Panel(data.spiketrains, xlabel=\"Time (ms)\", xticks=True, yticks=True),\n", ").save(figure_filename)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.spiketrains[10].as_array()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
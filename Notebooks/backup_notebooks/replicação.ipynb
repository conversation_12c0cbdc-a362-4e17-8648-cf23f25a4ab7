{"cells": [{"cell_type": "code", "id": "2250288c", "metadata": {"ExecuteTime": {"end_time": "2025-05-09T07:10:47.682497Z", "start_time": "2025-05-09T07:10:47.490667Z"}}, "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "sys.path.append('./../')\n", "from src.functions import plot_disparos_neuronios\n", "\n", "# Leitura dos dados\n", "data = pd.read_csv(f'./spikedata2.Umax=2000/Ca_cell_spike_ref_440_Umax=2000.csv', delimiter=',')\n", "data['spike_time'] = data['spike_time'].str.replace(' ms', '').astype('float')\n", "data = data.values\n", "\n", "# Definir intervalo de estado estacionário\n", "t_start, t_end = 4500, 54500  # ms\n", "window_duration = (t_end - t_start) / 1000  # s\n", "\n", "# Filtrar dados da fase de estado estacionário\n", "steady_data = data[(data[:, 1] >= t_start) & (data[:, 1] <= t_end)]\n", "\n", "# Seleção dos neurônios\n", "unique_neurons = np.unique(data[:, 0])\n", "selected_neurons = np.random.choice(unique_neurons, size=59)\n", "\n", "# Calcular taxa de disparo (spikes/s)\n", "firing_rates = []\n", "for neuron in selected_neurons:\n", "    n_spikes = np.sum(steady_data[:, 0] == neuron)\n", "    fr = n_spikes / window_duration\n", "    firing_rates.append(fr)\n", "\n", "# Plot agrupado\n", "x_pos = 0  # posição fixa no eixo X\n", "jitter = np.random.uniform(-0.05, 0.05, size=len(firing_rates))  # jitter discreto\n", "\n", "plt.figure(figsize=(5, 5))\n", "plt.scatter(np.full(len(firing_rates), x_pos) + jitter, firing_rates, color='royalblue', alpha=0.8, s=60)\n", "\n", "# Média e erro padrão\n", "mean_fr = np.mean(firing_rates)\n", "sem_fr = np.std(firing_rates) / np.sqrt(len(firing_rates))\n", "plt.plot(x_pos, mean_fr, '-', color='black', markersize=8)\n", "plt.errorbar(x_pos, mean_fr, yerr=sem_fr, color='black', capsize=5)\n", "\n", "# Customização do gráfico\n", "plt.xticks([x_pos], ['40% MVC - Controle'])\n", "plt.ylabel('Taxa de descarga (pps)')\n", "plt.xlim(-0.3, 0.3)  # zoom horizontal para manter os pontos agrupados\n", "plt.tight_layout()\n", "plt.show()"], "outputs": [{"data": {"text/plain": ["<Figure size 500x500 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 5}, {"cell_type": "code", "id": "1c95b7fd", "metadata": {"ExecuteTime": {"end_time": "2025-05-09T07:10:47.806822Z", "start_time": "2025-05-09T07:10:47.692287Z"}}, "source": ["#grafico para Umax=1600\n", "# Leitura dos dados\n", "data = pd.read_csv(f'./spikedata2.Umax=1600/Ca_cell_spike_ref_220_Umax=1600.csv', delimiter=',')\n", "data['spike_time'] = data['spike_time'].str.replace(' ms', '').astype('float')\n", "data = data.values\n", "\n", "# Definir intervalo de estado estacionário\n", "t_start, t_end = 4500, 54500  # ms\n", "window_duration = (t_end - t_start) / 1000  # s\n", "\n", "# Filtrar dados da fase de estado estacionário\n", "steady_data = data[(data[:, 1] >= t_start) & (data[:, 1] <= t_end)]\n", "\n", "# Seleção dos neurônios\n", "unique_neurons = np.unique(data[:, 0])\n", "selected_neurons = np.random.choice(unique_neurons, size=90)\n", "\n", "# Calcular taxa de disparo (spikes/s)\n", "firing_rates = []\n", "for neuron in selected_neurons:\n", "    n_spikes = np.sum(steady_data[:, 0] == neuron)\n", "    fr = n_spikes / window_duration\n", "    firing_rates.append(fr)\n", "\n", "# Plot agrupado\n", "x_pos = 0  # posição fixa no eixo X\n", "jitter = np.random.uniform(-0.05, 0.05, size=len(firing_rates))  # jitter discreto, não deixa os pontos aglomerados\n", "\n", "plt.figure(figsize=(5, 5))\n", "plt.scatter(np.full(len(firing_rates), x_pos) + jitter, firing_rates, color='royalblue', alpha=0.8, s=60)\n", "\n", "# Média e erro padrão\n", "mean_fr = np.mean(firing_rates)\n", "sem_fr = np.std(firing_rates) / np.sqrt(len(firing_rates))\n", "plt.plot(x_pos, mean_fr, '-', color='black', markersize=8)\n", "plt.errorbar(x_pos, mean_fr, yerr=sem_fr, color='black', capsize=5)\n", "\n", "# Customização do gráfico\n", "plt.xticks([x_pos], ['20% MVC - Controle'])\n", "plt.ylabel('Taxa de descarga (pps)')\n", "plt.xlim(-0.3, 0.3)  # zoom horizontal para manter os pontos agrupados\n", "plt.tight_layout()\n", "plt.show()"], "outputs": [{"data": {"text/plain": ["<Figure size 500x500 with 1 Axes>"], "image/png": "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**************************/+tGPuEWLyGC4H5ko9elO1C+++CIKCgowfvx42Gw22Gw2XHDBBcjPz8cLL7yQiBiJqJPC+5G9PhWKGjtZh/cjjx5i5wItIgPSva8hNzcX//jHP7Bv3z7s3r0bADB8+HAMHTo07sERUddxPzJRauvQ9qxUxu1ZRNyPTGQ0enKTYVaO/PKXv4QkSbj33nsjjzU2NmLevHnIyclBZmYmrrnmGi5YI+qEvGwzFszIwdI78zBrem9cN6UXZk3vjaV35WHBjBwmaSIDM8Snc9OmTfjDH/6AUaNGRT3+4x//GO+++y5WrVoFl8uFO++8E1dffTU+/vjjJEVKlNq4H5ko9ST9jvrkyZO46aab8Mc//hF9+vSJPO7xePCnP/0JTz/9NP7rv/4L48aNQ2lpKT755BN8+umnSYyYiIio+yQ9Uc+bNw/Tpk1rtbVry5YtCAaDUY8PHz4cZ555JtavX9/dYRIRESVFpxL1Rx99hJtvvhkTJkzAkSNHAAAvv/wy1q1bp+s8K1euxNatW7FkyZJWz1VWVsJqtUbqiYfl5+ejsrKyzXP6/X7U19dH/RAREaUq3Yn6jTfewNSpU+FwOFBWVga/3w9AG6p+/PHHO3yeQ4cO4Z577sErr7wCu92uN4w2LVmyBC6XK/IzYMCAuJ2biIiou+lO1I8++iief/55/PGPf4TFcmpRyqRJk6KqlZ3Oli1b4Ha7MXbsWJjNZpjNZqxduxa/+c1vYDabkZ+fj0AggOPHj0e9rqqqCgUFBW2ed+HChfB4PJGfQ4cO6X2LREREhqF71ffevXtx8cUXt3rc5XK1SqrtufTSS7Fjx46ox0pKSjB8+HDcf//9GDBgACwWC9asWYNrrrkmcu2DBw9iwoQJbZ43XC2NiIgoHehO1AUFBdi/fz/OOuusqMfXrVuHs88+u8PnycrKwsiRI6Mey8jIQE5OTuTxOXPm4L777kN2djZ69eqFu+66CxMmTMCFF16oN2wiIqKUpDtRz507F/fccw9efPFFSJKEiooKrF+/Hj/5yU+waNGiuAb361//GrIs45prroHf78fUqVPx3HPPxfUaRERERqa7hKgQAo8//jiWLFkCr9cLQBtu/slPfoJHHnkkIUF2BUuIEhGR0ejJTZ2u9R0IBLB//36cPHkSI0aMQGZmZqeCTTQmaiIiMho9uanTJUStVitGjBjR2ZcTERFRB3QoUV999dUdPuGbb77Z6WCIiIgoWof2UTcvINKrVy+sWbMGmzdvjjy/ZcsWrFmzBi4X+9kSERHFU4fuqEtLSyN/vv/++3Hdddfh+eefh8lkAgAoioI77riDc8BERERxpnsxWW5uLtatW4dhw4ZFPb53715MnDgRtbW1cQ2wq7iYjKjjjriDKCv3w+dX4bDJKBpqS1pbTCPFQhRvCV1MFgqFsGfPnlaJes+ePVBVVe/piMgA3HUhlK72YPv+RvgDApIkQQiBV60SRg22o2S6C3nZ3dO+3kixEBmB7t/2kpISzJkzB1988QUuuOACAMCGDRvwy1/+EiUlJXEPkIgSy10XwiMv1qCiOgSnQ0aOS44kR69fYONOHw67g1g0u2/CE6SRYiEyCt2/6U8++SQKCgrw1FNP4ejRowCAfv364X/+538wf/78uAdIRIlVutqDiuoQsl0mmGQp8rgkSciwS7BbJVRUh7D8XQ8WzMjpMbEQGYXuRC3LMhYsWIAFCxZEej1z7pcoNR1xB7F9fyOcDjkqMTZnkiU47TK27WvEEXcwYfPERoqFyEh0t7lsrlevXkzSRCmsrNwPf0DAaYudGMOcdgn+gEBZub9HxEJkJF1K1ESU2nx+FZIkQZLaT47aMdrxPSEWIiNhoibqwRw2GUIInG6XpnaMdnxPiIXISPibTtSDFQ21wWaV4PW3nxy9jQI2q4SiYbYeEQuRkTBRE/Vg/fMsGDXYDq9PhaLGTpCKKuBtVDF6iB39cxO3eMtIsRAZSac2IjY0NGDt2rU4ePAgAoFA1HN33313XAIjou5RMt2Fw+6gtnfZLsNpl07tXW7UEmNhrhmzpiW+lr+RYiEyCt0lRMvKynD55ZfD6/WioaEB2dnZqKmpgdPpRF5eHg4cOJCoWDuFJUSJTs9dp+1N3rYvXA0MEAKwWSWMHmLHrGndW5nMKLEQJYqe3KQ7UX/zm9/E0KFD8fzzz8PlcmHbtm2wWCy4+eabcc899+hqidkdmKiJOq5Vfe1htqQNMRspFqJ4S2ii7t27NzZs2IBhw4ahd+/eWL9+Pc455xxs2LABM2fOxJ49e7oUfLwxURMRkdHoyU26F5NZLBbIsvayvLw8HDx4EIDWs/rQoUOdCJeIiIjaonuip6ioCJs2bcKQIUNwySWX4MEHH0RNTQ1efvlljBw5MhExElGcsHUkUerRPfS9efNmnDhxAt/61rfgdrtxyy234JNPPsGQIUPwpz/9CWPGjElQqJ3DoW+itltH2tg6kigpEjpHnWqYqKmna9k60mmTolpHen3alqf2WkfGuhMHwLtzok7Sk5t0f4Xes2cPhg8fHvO5f/3rX5g6dareUxJRAnWldWSsO/GQoiIQFAAk2CwSTCYt6b/Ku3OihNC9mGzs2LH43e9+F/WY3+/HnXfeiSuvvDJugRFR13WmdWRY+E58404fZFlCjsuEXhky/AE0/Qj4/CpcGTJyXCbIsoSNO3145MUauOtC3fUWidKe7kS9fPlyPPjgg7j88stRVVWFzz77DEVFRfjPf/6Djz76KBExElEnlZX7cdKrwnNCwZHqII7WhOBtbN11KlbryOZ34hl2GZIkodYTQjAkYDYDFrOEkALUeJSmu3MZ2S5T5O68M464g1i97iRWranH6nUno744EPVUusenrrvuOkycOBElJSU499xz0dDQgFmzZuGpp56C0+lMRIxE1Am7DvjxwjvHcMLbfBmKwAmvCosZKMgxRzpQtWwdGetOPBAU8DUKyDIirShlWXtNMChgsUit7s47Omfd1mI3DqcTdaEpRyAQgKIoUBQF/fr1g91uj2dcRNQFuw748T/PulHfcCpJSxIQHvwOhoDD7lAkMbdsHVlW7oc/IOC0nRou9/pVqEI7T5gsSVBV7bmwWHfn7Yk1xB7+4XA6UScS9cqVK3HeeefB5XKhvLwc7777LpYtW4aLLrrIcHW+iXqqx5fXoNEvYDG1eEI6lbCFACprteTXsnWkz6823WWfyspCDb9eijofJEBtNpre8u78dGINsYfPE4/hdKJUp3ssac6cOXjyySfxox/9CADw3//939ixYwduv/12jBkzBvX19XEPkoja13z7lPtYCJV1CmQZkE0SZFVAFVpijuRYCYDQ7qxPehX4/ALFIx2RWtoOm9x0ly1OJU5Ze03zxyC0H1UAnpMKVBWQJIGQcuru/HRx613sxi1g1NPoTtRbt27FsGHDoh7r06cPXn/9dbz88stxC4yITi/W3O6JhnDC1JKz2awlZNGUrIHo4eua4wqGnGmNah1ZNNSGV60SvH6BDLt2sNMm45ikRCV8pelLgOek9jgk7c5bkoBNu3y4YIS93bnl8BB7jqv9pO60S6j1qCgr9zNRU4+je+i7ZZJubsaMGV0Khog6rq25XYtZy6JCAMGQlj0tZqD5DWvzMkeuLLlVsZP+eRaMGmyH16dCUbWDrRYJDrs2Jy2EgFC1O2dAS8xmkwSTrP3ZapXw+Rf+084txxpij0XvcDpROunUMsrDhw/jb3/7Gw4ePIhAIBD13NNPPx2XwIiofW0VMpGbff0WAggpAhazBItFS7Cqqo1YQwCKCoweEvuut2S6C4fdQa2imV2G0y4hx2VGIBhCIKjdSQPQ5sElQG06t8UsoSDbDFlGm4VUwmINscfScrEbUU+iO1GvWbMGV1xxBc4++2zs2bMHI0eOxFdffQUhBMaOHZuIGImohfbmdntlmHDCq93FStCGosPD1ZIkwdS0wCwUEjDJwGUTMmJeIy9bKyu6/F0Ptu1rRK1HhSQBNquW6AMB7ZxK002uLAEZDi2Zh+/qTze3HGuIPZaWi92IehLdiXrhwoX4yU9+gocffhhZWVl44403kJeXh5tuugmXXXZZImIkohbam9vNcMiwmICg0nTnDEBVBUym5iu4tbvfwlwTxg5ztHmdvGwzFszIaVXr+9gJBW9+UA+bRda+BMjaHLbVEp1sTze3HB5i37jTB7tVirmgTFEFvI1q1GI3op5Ed6LevXs3Xn31Ve3FZjN8Ph8yMzOxePFiXHnllZHV4ESUOKeb283pbUJlrRL5u6oCJpOWoBVV+7vdJmHhzL4dul7/PEtUol21ph5mk4zeWS33f0XryNxyrCH2SNOQRi1JF+aaoxa7EfUkuid8MjIyIvPS/fr1wxdffBF5rqamJn6REVGbms/tNhcMCVTWBlF7XImaq1aFVps70LSuqzDXhF/dlYcRZ3duKLmt67fUkbnl8BB78UgHVCFQ61FQ6wmh1qNAFdq2sfY6exGluw7/5i9evBjz58/HhRdeiHXr1uGcc87B5Zdfjvnz52PHjh148803ceGFFyYyViJqEmtuNxgSOFqj1eKWZcDUVNhEUbUFXwLasPhd1/XGf43PbPPcR48exdGjR9u9vrkxhIbaOniPSXBYm+ajs/KR0Ss/6riOzi23NcReNMzG4W7q8Trcj9pkMuHo0aM4efIkTp48iVGjRqGhoQHz58/HJ598giFDhuDpp5/GwIEDEx2zLuxHTenqiT/XYuNOX2TVd2VtEA0+LUmHh7cBbcGXLJ36/wmjHHjo1tw2z/uLX/wCDz/8sO54xl06H+On/CTyd0UVqPMoKB7paHPVN1FPpSc3dThRy7KMyspK5OXlxSXI7sJETekqvI+6ojoEq0VCrUebk1bU6MImFrMECdr2KUXR5qp/dVceRg2JXZ8/1h21z+fD5MmTAQDr1q2Dw+FAnSeEP75zHNXHFNhtMnJy8pHhKmg1t8xha6LWEpaoq6qqkJvb9jdxI2KipnTmrtP2Ka/f4UV9g4gqZCLLWhGS5uvNhCoQVIARg6x49icFHb5OQ0MDMjO14fKTJ08iIyMj6vrb9oUro2lfEmxWCaOH2DFrGrteEcWiJzfp+gQNHTr0tBWE6urq9JySiLogPLf7wtsy3l57Eo1BAQmAqUWCDpNkCZIicLAqGJe62ZxbJko8XYn64YcfhsvFLRJERlPQ16IlZgGYzc36WbagVQADFAVxrZvdcvsWEcWPrkR9ww03pNwcNVFPUDTUBtkkAZJoM0kD2rC0LAFWM+tmE6WKDu+jPt2QNxElT/88CwbkmbVyoWrsZSfhOt92mwRZllg3myhFdPiT2sE1Z0SUJLdd1RtmMxBStJKh4c+slqC1Fd8Ws9bbmXWziVJHhxO1qqoc9iYysFFD7JhwngNmkzbErSha4w1FASC0hhn52SYEggKjh9i52IsoRXDfBFEaueOaPqj1KDhUFYQsSTCb0TTMLSEUAuobWDebKNVwkooojYTrZk8c5YTDLkFVAUURONGgsm42UYrip5UozXBvM1F6YaImSlPc20yUHjj0TUREZGBM1ERERAbGRE1ERGRgTNREREQGxkRNRERkYEzUREREBsbtWUQ9RKt91UNtht6+lWrxEiUKEzVRmnPXhVC62oPt+xvhDwhIkgQhBF61Shg12I6S6S5DVSpLtXiJEo2/7URpzF0XwiMv1qCiOgSnQ0aOS44kPq9fYONOHw67g4YpK5pq8RJ1B85RE6Wx0tUeVFSHkO0yIcMuR/rKS5KEDLuMbJcJFdUhLH/Xk+RINakWL1F3YKImSlNH3EFs398Ip0OGSZZiHmOStf7U2/Y14og72M0RRku1eIm6CxM1UZoqK/fDHxBw2mInvTCnXYI/IFBW7u+myGJLtXiJugsneYjSlM+vQpKkyPBxW7RjtOMB4OjRozh69Gj0uXy+yJ8/++wzOByOVufp168f+vXr1+3xEqU7JmqiNOWwyRBCQAjRbvLTjtGOB4A//OEPePjhh9s8fvLkyTEff+ihh/CLX/yi2+MlSndM1ERpqmioDa9aJXj9Ahn2thOft1HAZpVQNMwGALj99ttxxRVX6L5eV+6mgc7HS5TumKiJ0lT/PAtGDbZj404f7FYp5gItRRXwNqooHulA/1ytmEhXh7A7q7PxEqU7JmqiNFYy3YXD7qC2L9kuw2mXTu1LbtSSXmGuGbOmuVq9NpGVwdo6d1fiJUpXkhBCJDuIRKqvr4fL5YLH40GvXr2SHQ5Rt3PXafuOt+0LV/oChABsVgmjh9gxa1p0pa+2KoPZ4lAZrCPnBqArXqJUpCc3MVET9RCt7mKH2VoNH7esDOa0SVGVwbw+7Y62M5XB9J67I/ESpSo9uYlfS4l6iP55ltMOXTevDNZ8jlirDCbBbpUilcEWzMjRdX295+5IvEQ9Afc3EBGAxFYGY9Uxos5joiYiAImtDMaqY0Sdx0RNRAASWxmMVceIOo+JmogARFcGa09nKoMl8txE6Y6fBiICoFUGszVVBmtPZyqDJfLcROkuqYl6yZIlOP/885GVlYW8vDxcddVV2Lt3b9QxjY2NmDdvHnJycpCZmYlrrrkGVVVVSYqYKH2FK4N5fSoUNXZCDVcGGz3ErmurVCLPTZTukpqo165di3nz5uHTTz/Fv//9bwSDQXz7299GQ0ND5Jgf//jH+Pvf/45Vq1Zh7dq1qKiowNVXX53EqInSV8l0FwpzzajzKGjwqZGhaiEEGnwq6jxKpyuDJfLcROnMUAVPqqurkZeXh7Vr1+Liiy+Gx+NBbm4uVqxYge9///sAgD179uCcc87B+vXrceGFF572nCx4QqSP3kpmRjk3USpJ2YInHo8HAJCdnQ0A2LJlC4LBIKZMmRI5Zvjw4TjzzDPbTNR+vx9+/6mtHfX19QmOmii95GWbsWBGTkIqgyXy3ETpyjCJWlVV3HvvvZg0aRJGjhwJAKisrITVakXv3r2jjs3Pz0dlZWXM8yxZsqTdXrpE1DGJrAzGqmNEHWeYVd/z5s3D559/jpUrV3bpPAsXLoTH44n8HDp0KE4REhERdT9D3FHfeeedWL16NT788EOcccYZkccLCgoQCARw/PjxqLvqqqoqFBQUxDyXzWaDzcatHURElB6SekcthMCdd96Jt956C++//z4GDRoU9fy4ceNgsViwZs2ayGN79+7FwYMHMWHChO4Ol4iIqNsl9Y563rx5WLFiBd555x1kZWVF5p1dLhccDgdcLhfmzJmD++67D9nZ2ejVqxfuuusuTJgwoUMrvokotbRaZDbUxrls6vGSuj2rrbq/paWlmDVrFgCt4Mn8+fPx6quvwu/3Y+rUqXjuuefaHPpuiduziIzPXRdC6WoPtu8Pb9vS+lTbrBJGDbajZDq3bVF60ZObDLWPOhGYqImMzV0XwiMv1qCiOgSnQ4bTJkUStdcv4PWpKMw1Y9HsvkzWlDb05CbDrPomop6pdLUHFdUhZLtMyLDLkZE2SZKQYZeR7TKholorlELUE/HrKVEKSbc53CPuILbvb4TTIcMkx54KM8kSnHYZ2/Y14og7mNLvl6gzmKiJUkBbc7ivpvgcblm5H/6AQI6r/cE9p11CrUdFWbmfiZp6nNT7ZBP1MC3ncHNcctQc7sadPhx2B1NyDtfnVyFJUpsLS8O0Y7TjiXoazlETGVw6z+E6bDKEEDjdmlbtGO14op6Gv/VEBtaZOdxUUjTUBptVgtfffqL2NmpbtYqGseog9TxM1EQGFp7DddraHxp22iX4AwJl5f52jzOa/nkWjBpsh9enQlFjJ2tFFfA2qhg9xM4OW9QjMVETGVhPmMMtme5CYa4ZdR4FDT41MgwuhECDT0WdR0FhrhmzprmSHClRcjBRExlYT5jDzcvWipkUj3RAFQK1HgW1nhBqPQpUIVA80pGSC+WI4oW/+UQGVjTUhleb5nAz7G3fVaf6HG5ethkLZuS03ic+zMbhburxmKiJDCw8h7txpw92qxRzQVl4Drd4pCPlk1r/PAv3SRO1kHrjZEQ9DOdwiXo2Jmoig+McLlHPxk82UQrgHC5Rz8VETZRCOIdL1PNw6JuIiMjAmKiJiIgMjImaiIjIwJioiYiIDIyJmoiIyMCYqImIiAyM27OIKC5a7fEeauNWMqI4YKImoi5x14VQutqD7fsb4Q8ISJIEIQRetUoYNdiOkukuVk0j6gJ+eoio09x1ITzyYg0qqkNwOmTkuORIovb6BTbu9OGwO8gSp0RdwDlqIuq00tUeHKoKwWwCAgGB+gYVwaB2V51hl5HtMqGiOoTl73qSHSpRyuJXXKI00d1zxNv3NeLDMi8CQYEGHyAACAFIkgKrWUJubxMcdhk2i4T123144Z1jKMixtBkX57iJYpNEuGdemqqvr4fL5YLH40GvXr2SHQ5R3LU1R2xL4Byxuy6EHz9ThcpaBbKkJWg1xn9JzKZTzzntEmwWqVVcyYifKNn05Cb+9hOlsGTNEZeu9uD4CRUStCTc/Ou+JJ36e0gBpKbHnDYZvbPkqLh+dHUf/P7NY5zjJmoH56iJUljpag8qqkPIdpmQYdeSHICEzhEfcQexfX8jbFYpKklLkvbTkoCWzCW5dVyPv1TT7fETpRomaqIUFU6YTocMkxwjQwIwyRKcdhnb9jXiiDsYl+uWlfvhDwg4baeuGStBt2QxnTrIJEuwWiRU1SqwWaRujZ8o1TBRE6WoWAkzFqddgj8gUFbuj8t1fX4VkiQhpLZxBx1jrlqSgKAiWh2nCkA9zTKZeMdPlGqYqIlSVDhhSqe5ndWO0Y6PB4dNhhACqiogS83mpNvJt7IEiBaXD+dnIbo3fqJUw0RNlKLCCfN0Gze0Y7Tj46FoqA02q4SQov3dbAJkOXyt1sebTdr/Sy0uH/5+IUndGz9RquFvPlGKCidMr7/9ROdt1LY6FQ2zxeW6/fMsGDXYDlUVkWRrMUuwWCSYTYCp2X9VZFlLyHLTqu/mwo/LbcxPJyp+olTDRE2UosIJ0+tTocTaxAxAUQW8jSpGD7Gjf278ioeUTHdhQL4FsgQoCiCakrZJliLJGQBMEqCqgMOuLR5rHlcgKJCfY4I/ILo9fqJUwkRNlMJKprtQmGtGnUdBg0+NDIMLIdDgU1HnUVCYa8asaa64Xjcv24xFs/tiwigHTCYgGAKCIYFgSLu+0w5YzNo+apMMZPcyxYzrZzP7JiV+olTCymREKc5dp+0z3rYvXNlLmyu2WSWMHmLHrGmJrey1fV8j/vjOcRysCkFVBCwWCbIEmE0SLGYtgYcUtBlXsuMnSgY9uYmJmihNtKqVPczWrcPFbV2/o3ElO36i7sRE3QwTNRERGY2e3MQ5aiIiIgNjoiYiIjIwrtAgSmHs4UyU/pioiVJQWz2cX2UPZ6K0w08yUYpJVg9qIkoOzlETpZhk9KAmouTh122iFNKZHtR656yPuIN4f7MXB44EAABnn2HFf41zcu6bKEmYqIlSSLgHdY6r/cEwp11CrUdFWbm/wwnWXRfCc28cw6Zd2rx3uMDCR5/5sPL/PDh/hAN3XNOHw+lE3YyfOKIUkqge1O66EB5cVo0vDgchhNb1yiRLEBAQKuAPAB9v86GyNoTFt+UyWRN1I85RE6WQRPWgLl3twVcVWpK2mCWYTBIgaQlfNkkwmwEB4KuKIOe+iboZvxYTpZCioTa82tSDOsPe9l21nh7OR9xBbN3bCEUFTCYAMU4rSRJMsoCiAlv2+Nqc++a+bqL4Y6ImSiHhHtQbd/pgt0oxF5SFezgXj3R0qKlFWbkfXp+qDXm3M6QefqrBJ1rNfXNfN1Hi8JNDlGJKprtw2B3U9lHbZTjt0ql91I1aktbTw9nnVyEASDJi3k2HafPiIvKaMO7rJkoszlETpZi8bDMWze6L4pEOqEKg1qOg1hNCrUeBKgSKRzp0JUWHTYYEQKgA2pn6bj4v3nzum/u6iRKLX2+JUlBethkLZuTEpYdz0VAbnA4ZjQEt0bc1/B3O0xmOU3Pf3bGvm6inY6ImSmH98yxdTnz98ywYO8yODzY3IKQAsoRWQ+BCCKgqYJKBccNPzX0ncl83EWk49E1EKJnuwlmFFkgSEAwJKIoARFOCVgRCIe24swotUXPfidrXTUSnMFETEfKyzVh8Wy4mj3HAZpWgqkAgJBAMAYoK2KzA5DGOVsVOErWvm4hO4dA3EQHQkvVDt+biiDuID7Z48cXhACAB3+hvxbfGO2POfRcNteFlk4Tq4wosJgmyDDhtMiwWCYGggNevQqjaXbrFgg7t6yaiaEzURBSlf54FN3/n9Fu73HUh/OW9epz0Kmj0a9u7JACSpCC8kSt8o60KwG6V8Jd/1nNPNZFO/LQQkW7N9073yjRBCBXBkIAEIKScOs7UNNJtNQOuTJl7qok6gZ8UIoPSW44zHuU7O3qO5nunTbIEp01GrSeEEw3Rc9WqALKcMvq6TDCbJSgOEdlTvWBGTodjAMDSpNRjMVETGYzecpzxKN+p5xyx9k5bzBKye5nhbQxCFdoWLyG0sqM5vbQkDbS/pzpWDCFFRSAoAEiwWbRmISxNSj0Nl2ASGUh4SHnjTh9kWUKOyxT5kWUJG3f68MiLNXDXhTp1fDyuGd477bRFb8ny+rV64WYTYDJJMJskCKE93pzTLsEf0OqFtxdDrwwZ/gCafgR8fhWuDFnXeyNKB0zURAaitxxnPMp36j1HW3unhYpIa0ztBNqP2mLrdKw91bFiqPWEEAwJmM3aHXtIAWo8CkuTUo/DRE1kEHrLcW7d7dNdvrOlrbt9+HSHD6oKnPSqCAZb74dueY629k6rQkvWoaaCKULViqbILf4rEwioaPQL7PrSj9XrTsZ8H4GggK9RQJabEr+kncfnPxXj6d4bUbrg5A6RQegtx/nehoZOl+8Mzwd/+rkX9V4BSQJO+gBZVuCwnVr8FescLXtiB0MCtZ4QvD4BVSCqsYckAVaLdp5QSKDGo6ChqaXmtnI/duz3Q1EAX0BFYd9T/zny+lWo4tSqcUBrwRlStL3ZLoupzfdGlG54R01kEHrLcXp9nSvf2Xw+WECCLGlDy2aTdp4Gn4qKmhBCIRHzHOGe2F6fCn9AxdGaEBp8QttHHSOU6mMKfH7tnCe9WpLOcEjIyzYjx6Ul3GAIqKxVEGy6ZqthdCDmUDpLk1JPwDtqIoNoPqQcTlDBpupeqopI1S+zWVtR7XS0Pj6s+eskSSCknCrf2Xw++IRXRYMXkXPIkpa4g013vwU52n8iWpYADffE3ncwgJCiLSCTZAkWaGVHwyleAuAPChxxhyCAyJeCvr2180qSBLtNQn2DdlxFTRCuTBMUcarWeOS9Nd2tNx9KZ2lS6gmYqIkMovmQss2sLZzyNSXbcKkvWVZgMUvIdMi47MIM7DzgjwxBA6eGl5u/Tqjane6mXT6cmW+Omg922mQck5TIVioAreaDLRYJ3kYBm/VUe8u8bDPmXtkbC5+rhqIKKCq0bw9NiVQV2l/VpowdTtyyDOT1McHSNKweDAltKLzpIH8AqD2uQGq6c5YkwKTddGstOGXAaT+VlFvGRZSOmKiJDCI8pPzpDh9qAqrWclKGNiTdlKgVVVtkZTWrOKPp+I07fbBbJQgVqKjRVkqHXycgoAjAapXw+Rd+7D8cgK9RILePluysFgkOu6QNXUun7l6bzwdnmmR4G1UUj3RE1fs+WBWCwyYhv48JvoDWBlMI4IRXRUjRhsIBramHENpbUAXgPqagsK8EAeBoU7ySdKrcKKAlaAGtypkkaT2yVRXIcMiRJK+oImZcROmG40VEBlIy3QVJAgJBbZg4krBCAkFFu3O1WLSktvxdD0qmu1CYa0adR0FlXQiBoIDZpM3bqkJAUbSh5oJsM7JdJhw/ocLnjx4qz3GZYTFLUBRAVZtWczddt9EvUOdRUJhrjmpvCQCVNUH4gwINjVqGzXTICIQEQooWg6npJzxUbTJpXx7Cw+rh7VcmkxZj8xFuWZJgabqNCIa0O2+zCejrMkEI7S68rbiI0g0TNZGBBEMCVgtgt2t3nMGQtlVJURG5Y1VVIKgAW/c0IhgSWDS7L877hg3+gJZgQ4qWoNG0aKtfXy0Rm2StulcwJBAInFp8ZTFrx2Q4tOysKNoXg/AdbvFIR1RtbnddCA8uq8Zba0+gwacl3epjCg65gzjhVbWEG2NRmdAKjEGWAW+jCm+z7VeSBFjM2hcTrV64iFQ4k6B9OXHYZHgaFNR6FKhCtIqLKF3xN5zIQMrK/QgpQEEfE47UhKA0LSKTmxKcBAmqEGj0CwSCCj4s8+LGqS6MH+HAtv2NsFlkbZi5aeFZeGtUmCtTRn2DiuMNKvKsp76nW8wSCnIskdaUjX5tKHzRnByMHeaIHLd2SwOW/qUWjaeKioWnpqE2NeMIx9y87glw6u+yJCGgaA08zObo85hNQH62GcGQiCyE8weBa76Vhd5ZplO1vofZONxNPQYTNZGBhLdo1darUBTAatburFVVNG1LEpBlbVg4EBL4z6YGZGWY8OkOL0IKkJ2l9YIGtJXfnpNK1Ipxm1WGxayV5FRUESkw0nyVeEgVCAQFBvYzo6JaAVQf9hwM4P3NDfjySKj5NumYhNBGBkxyUxJvujPW5qpFU71u7VhFBSQIyE2LxzIcctRiMQCo9YRgs0qYPjkzrv/WRKmCiZrIQBw2WVvE1agNIQcV0aoEJ5RTqfKroyH86W/HEQiq8PmBrxuDsJi1QiFB5dSeY23YWYGlaZ7YlWlCnUeB2QTUN6hRx4YdOBzEs6/XRRaDidNl6GaEiG532Tx0pWkpuIA2zB4mS0DvzOgkze1XREzURIZSNNQGNM1DC5w+OQoBNPpVbXsUtFXV/qZqmlL4f5qGpiEAv6rtVz6zQEIgBFQfj10oRIKW6LtLeF7bfUxBv75SZGU3t18Rpchist/97nc466yzYLfbUVxcjI0bNyY7JKKE6J9nQUFfU2QfckdEEnOLBVzhRC9iPLf7qyBqmpK0SW699kvHzXOXWc3aNjGzCZFypMCp7Vejh9g5H009muET9WuvvYb77rsPDz30ELZu3YrRo0dj6tSpcLvdyQ6NKCFGDbHrfo3atBCrLeGk3zJhhwuKCMR+PpEiK7xVbQ5egtS0IlybW+f2KyKN4RP1008/jblz56KkpAQjRozA888/D6fTiRdffDHZoRElhKrov58V6qkqYG2RpdbJXAhEhs27k9R0bUnWFswBTdvKVC2exoDK7VdETQz9CQgEAtiyZQsWLlwYeUyWZUyZMgXr16+P+Rq/3w+//9Tekfr6+oTHSRRPNcf1Tw4LNDWyaEO42IgS40tA+HXdeTcdnjuHALKcMjIdcmTVudevYtqkTNx6ZZ9uDIjIuAx9R11TUwNFUZCfnx/1eH5+PiorK2O+ZsmSJXC5XJGfAQMGdEeoRHHVrUmziZ5V3R0lSe2/FwFt65jFoq1E750lw2bR9nQTkcbQibozFi5cCI/HE/k5dOhQskMi0uXs/lato6POZC3FWBQWea6913Xzl4Lm15MlNtkgOh1DJ+q+ffvCZDKhqqoq6vGqqioUFBTEfI3NZkOvXr2ifohSyX+Nd8JmlXQtvZYAmGQp0gijpXC97Ui1sGZ3uuHKZwCiu2iFXxuHRN78nKJpyBsAHDapVZMNrvImimboRG21WjFu3DisWbMm8piqqlizZg0mTJiQxMiIEqd/ngXnj7BH9V0G2h9GDt9Nx0qq4cfC1c1MMgARTu6IVC6TmiXr6JO3H68kNZ2z2d+bJ/7mxVKiG28AeX3MbLJBdBqGXkwGAPfddx9mzpyJ8ePH44ILLsAzzzyDhoYGlJSUJDs0ooS545o+qKwN4cuKIILatuL255DDZTtNQIYDUEJAY/DU6xQlPMwswW6RUHdCW0HWJ0uGP6jVDg+v7wqzmAA0lfa0mLX/b76yXGr2h/D2sByXjJACeE6qEEr0tq/wSm9AiyXTqTXZEAKwWSUUj3Rg1jQXV3kTtWD4T8T111+P6upqPPjgg6isrMSYMWPw3nvvtVpgRpRO8rLNWHxbLpa/68HGXT54TqhakmzqQGUxS+idKeOsfmYcP6HiaJ0CVRGwWCTIEmDLkDAgz4LjJxUcrQ011Q0HZFmC2SJh8hit0Ub5wQBUod0RB0La6vD8bDPGDbMj22WCwyajsK8JFTUKqo+FsHVvI9zHQk0tMbUvB7JJwpn5Zsy9qjdGDbbjiDuID8u8+PAzLyprtE5XqtBWl1vMEkYPseHqb2XhYFWITTaIOkASIhFrPY2jvr4eLpcLHo+H89WUko64gygr96OyNgj3MQV5fUwoyLFEJbfwMbESX2efO108HXlNZ85P1BPoyU1M1ERERN1MT24y9GIyIiKino6JmoiIyMCYqImIiAyMiZqIiMjAmKiJiIgMjImaiIjIwJioiYiIDIyJmoiIyMCYqImIiAyMiZqIiMjAmKiJiIgMzPDds7oqXMq8vr4+yZEQERFpwjmpI+020j5RnzhxAgAwYMCAJEdCREQU7cSJE3C5XO0ek/bds1RVRUVFBbKysiBJ0ulfQEQx1dfXY8CAATh06BA70RF1kRACJ06cQGFhIWS5/VnotE/URBQfbBlLlBxcTEZERGRgTNREREQGxkRNRB1is9nw0EMPwWazJTsUoh6Fc9REREQGxjtqIiIiA2OiJiIiMjAmaiIiIgNjoiYiamH58uXo3bt3ssMgAsBETdSuJUuW4Pzzz0dWVhby8vJw1VVXYe/evVHHNDY2Yt68ecjJyUFmZiauueYaVFVVRZ6vq6vDd7/7XWRmZqKoqAhlZWVRr583bx6eeuqp08Yya9YsSJKEH/7wh62emzdvHiRJwqxZswAA3/3ud3HZZZfFPM9HH30ESZKwffv2yGNvvPEGvvnNb8LlciEzMxOjRo3C4sWLUVdXd9q4Tmf//v0oKSnBGWecAZvNhkGDBuHGG2/E5s2bu3zu5s466yw888wzcT0nkREwURO1Y+3atZg3bx4+/fRT/Pvf/0YwGMS3v/1tNDQ0RI758Y9/jL///e9YtWoV1q5di4qKClx99dWR5x977DGcOHECW7duxTe/+U3MnTs38tynn36KDRs24N577+1QPAMGDMDKlSvh8/kijzU2NmLFihU488wzI4/NmTMH//73v3H48OFW5ygtLcX48eMxatQoAMDPf/5zXH/99Tj//PPxz3/+E59//jmeeuopbNu2DS+//HKH/61i2bx5M8aNG4fy8nL84Q9/wK5du/DWW29h+PDhmD9/fpfO3RmKokBV1W6/LlGXCCLqMLfbLQCItWvXCiGEOH78uLBYLGLVqlWRY3bv3i0AiPXr1wshhPjOd74jfv/73wshhNi1a5dwOp1CCCECgYAYPXq02LRpU4euPXPmTHHllVeKkSNHir/85S+Rx1955RUxatQoceWVV4qZM2cKIYQIBoMiPz9fPPLII1HnOHHihMjMzIzEs2HDBgFAPPPMMzGveezYsQ7FFouqquLcc88V48aNE4qitHvu7du3i29961vCbreL7OxsMXfuXHHixInI8+H3/qtf/UoUFBSI7Oxscccdd4hAICCEEOKSSy4RAKJ+hBCitLRUuFwu8c4774hzzjlHmEwm8eWXX4q6ujoxY8YM0bt3b+FwOMRll10mysvLI9cLv665t99+WxQVFQmbzSYGDRokfvGLX4hgMNjpfx+ijuIdNZEOHo8HAJCdnQ0A2LJlC4LBIKZMmRI5Zvjw4TjzzDOxfv16AMDo0aPx/vvvIxQK4V//+lfkTnbp0qX45je/ifHjx+uKYfbs2SgtLY38/cUXX0RJSUnUMWazGbfccguWL18e1UZv1apVUBQFN954IwDglVdeQWZmJu64446Y1+rKPO1nn32GnTt3Yv78+TGbDoTP3dDQgKlTp6JPnz7YtGkTVq1ahf/85z+48847o47/4IMP8MUXX+CDDz7ASy+9hOXLl2P58uUAgDfffBNnnHEGFi9ejKNHj+Lo0aOR13m9XjzxxBN44YUXsHPnTuTl5WHWrFnYvHkz/va3v2H9+vUQQuDyyy9HMBiM+V4++ugj3HLLLbjnnnuwa9cu/OEPf8Dy5cvx2GOPdfrfh6jDkv1NgShVKIoipk2bJiZNmhR57JVXXhFWq7XVseeff75YsGCBEEK7677xxhvFmWeeKS6++GKxc+dOUV5eLoYMGSJqamrE7bffLgYNGiSuvfZacfz48TavH76rdLvdwmazia+++kp89dVXwm63i+rq6qg7aiFO3dl/8MEHkccuuugicfPNN0f+/p3vfEeMGjWqC/8qbXvttdcEALF169Z2j1u2bJno06ePOHnyZOSxd999V8iyLCorK4UQ2nsfOHCgCIVCkWOuvfZacf3110f+PnDgQPHrX/866tylpaUCgPjss88ij5WXlwsA4uOPP448VlNTIxwOh3j99dcjr2t+R33ppZeKxx9/POrcL7/8sujXr99p/hWIuo531EQdNG/ePHz++edYuXKlrte5XC6sWLECX3/9NdauXYsRI0bg9ttvx69+9Su88sorOHDgAPbu3Qun04nFixef9ny5ubmYNm0ali9fjtLSUkybNg19+/Ztddzw4cMxceJEvPjiiwC0RV0fffQR5syZEzlGdLIw4Xe+8x1kZmYiMzMT5557bsxjOnru3bt3Y/To0cjIyIg8NmnSJKiqGrVw79xzz4XJZIr8vV+/fnC73ac9v9VqjYxihK9nNptRXFwceSwnJwfDhg3D7t27Y55j27ZtWLx4ceQ9Z2ZmYu7cuTh69Ci8Xm+H3idRZ5mTHQBRKrjzzjuxevVqfPjhhzjjjDMijxcUFCAQCOD48eNRw8RVVVUoKCiIea7S0lL07t0bV155Ja6++mpcddVVsFgsuPbaa/Hggw92KJ7Zs2dHhoZ/97vftXncnDlzcNddd+F3v/sdSktL8Y1vfAOXXHJJ5PmhQ4di3bp1CAaDsFgsHbo2ALzwwguRBW1tvW7o0KEAgD179qCoqKjD525Ly+tIktShhWEOh6PLvehPnjyJhx9+OGqRYJjdbu/SuYlOh3fURO0QQuDOO+/EW2+9hffffx+DBg2Ken7cuHGwWCxYs2ZN5LG9e/fi4MGDmDBhQqvzVVdXY/HixXj22WcBaKuQw/OiwWAQiqJ0KK7LLrsMgUAAwWAQU6dObfO46667DrIsY8WKFfjzn/+M2bNnRyWtH/zgBzh58iSee+65mK8/fvx4zMf79++PwYMHY/DgwRg4cGDMY8aMGYMRI0bgqaeeiplQw+c+55xzsG3btqiV9B9//DFkWcawYcPafG8tWa3WDv37nXPOOQiFQtiwYUPksdraWuzduxcjRoyI+ZqxY8di7969kffc/CfW/DtRPPE3jKgd8+bNw1/+8hesWLECWVlZqKysRGVlZeRu0uVyYc6cObjvvvvwwQcfYMuWLSgpKcGECRNw4YUXtjrfvffei/nz56N///4AtCHel19+Gbt378ayZcswadKkDsVlMpmwe/du7Nq1K2o4uKXMzExcf/31WLhwIY4ePRrZZx1WXFyMBQsWYP78+ViwYAHWr1+Pr7/+GmvWrMG1116Ll156qYP/Uq1JkoTS0lKUl5fjoosuwj/+8Q8cOHAA27dvx2OPPYYrr7wSAHDTTTfBbrdj5syZ+Pzzz/HBBx/grrvuwowZM5Cfn9/h65111ln48MMPceTIEdTU1LR53JAhQ3DllVdi7ty5WLduHbZt24abb74Z/fv3j8TU0oMPPog///nPePjhh7Fz507s3r0bK1euxAMPPKDvH4WoM5I8R05kaGix5Sf8U1paGjnG5/OJO+64Q/Tp00c4nU7xve99Txw9erTVud577z1xwQUXRG1VamhoENdee63IysoSl156qaiqqmozlvBisra0XEwW9sknnwgA4vLLL2/zta+99pq4+OKLRVZWlsjIyBCjRo0Sixcv7tL2rLC9e/eKW265RRQWFgqr1SoGDhwobrzxxqhFZh3dntXcPffcIy655JLI39evXy9GjRolbDZbq+1ZLYW3Z7lcLuFwOMTUqVNPuz3rvffeExMnThQOh0P06tVLXHDBBWLZsmWd/4ch6iC2uSQiIjIwDn0TEREZGBM1ERGRgTFRExERGRgTNRERkYExURMRERkYEzUREZGBMVETEREZGBM1ERGRgTFRExERGRgTNRERkYExURMRERkYEzUREZGB/X9Cpa04Y0rl/AAAAABJRU5ErkJggg=="}, "metadata": {}, "output_type": "display_data"}], "execution_count": 6}, {"cell_type": "code", "id": "8f8356a1", "metadata": {"ExecuteTime": {"end_time": "2025-05-09T07:10:48.108559Z", "start_time": "2025-05-09T07:10:47.818161Z"}}, "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import sys\n", "sys.path.append('./../')\n", "from src.functions import plot_disparos_neuronios\n", "\n", "# === Leitura dos dados ===\n", "data = pd.read_csv(f'./spikedata2.Umax=1600/Ca_cell_spike_ref_220_Umax=1600.csv', delimiter=',')\n", "data['spike_time'] = data['spike_time'].str.replace(' ms', '').astype('float')\n", "data = data.values\n", "\n", "data1 = pd.read_csv(f'./spikedata2.Umax=1600/Ca_cell_spike_ref_440_Umax=1600.csv', delimiter=',')\n", "data1['spike_time'] = data1['spike_time'].str.replace(' ms', '').astype('float')\n", "data1 = data1.values \n", "\n", "# === Intervalos de estado estacionário ===\n", "# 20% MVC\n", "t_start_20, t_end_20 = 4500, 54500  # ms\n", "window_20 = (t_end_20 - t_start_20) / 1000  # s\n", "steady_data_20 = data[(data[:, 1] >= t_start_20) & (data[:, 1] <= t_end_20)]\n", "\n", "# 40% MVC\n", "t_start_40, t_end_40 = 4500, 54500  # ms \n", "window_40 = (t_end_40 - t_start_40) / 1000  # s\n", "steady_data_40 = data1[(data1[:, 1] >= t_start_40) & (data1[:, 1] <= t_end_40)]\n", "\n", "# === Seleção de neurônios ===\n", "unique_neurons = np.unique(data[:, 0])\n", "neurons_20 = np.random.choice(unique_neurons, size=90)\n", "neurons_40 = np.random.choice(unique_neurons, size=59)\n", "\n", "# === Calcular taxa de disparo (spikes/s) ===\n", "# 20%\n", "fr_20 = []\n", "for neuron in neurons_20:\n", "    n_spikes = np.sum(steady_data_20[:, 0] == neuron)\n", "    fr = n_spikes / window_20\n", "    fr_20.append(fr)\n", "\n", "# 40%\n", "fr_40 = []\n", "for neuron in neurons_40:\n", "    n_spikes = np.sum(steady_data_40[:, 0] == neuron)\n", "    fr = n_spikes / window_40\n", "    fr_40.append(fr)\n", "\n", "# === Plot ===\n", "plt.figure(figsize=(6, 5))\n", "\n", "# Posições no eixo X\n", "x_20 = 0\n", "x_40 = 0.7\n", "\n", "# Jitter\n", "jitter_20 = np.random.uniform(-0.05, 0.05, size=len(fr_20))\n", "jitter_40 = np.random.uniform(-0.05, 0.05, size=len(fr_40))\n", "\n", "# Pontos\n", "plt.scatter(np.full(len(fr_20), x_20) + jitter_20, fr_20, color='royalblue', alpha=0.8, s=60, label='20% MVC')\n", "plt.scatter(np.full(len(fr_40), x_40) + jitter_40, fr_40, color='tomato', alpha=0.8, s=60, label='40% MVC')\n", "\n", "# Médias e barras de erro\n", "mean_20 = np.mean(fr_20)\n", "sem_20 = np.std(fr_20) / np.sqrt(len(fr_20))\n", "plt.errorbar(x_20, mean_20, yerr=sem_20, fmt='_', color='black', capsize=5, markersize=14, elinewidth=1.5)\n", "\n", "mean_40 = np.mean(fr_40)\n", "sem_40 = np.std(fr_40) / np.sqrt(len(fr_40))\n", "plt.errorbar(x_40, mean_40, yerr=sem_40, fmt='_', color='black', capsize=5, markersize=14, elinewidth=1.5)\n", "\n", "# Customizações\n", "plt.xticks([x_20, x_40], ['20% MVC - Controle', '40% MVC - Controle'])\n", "plt.ylabel('Taxa de descarga (pps)')\n", "plt.title('Taxa de descarga na fase de estado estacionário')\n", "plt.xlim(-0.3, 1.0)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"], "outputs": [{"data": {"text/plain": ["<Figure size 600x500 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 7}, {"cell_type": "code", "id": "f4972940", "metadata": {"ExecuteTime": {"end_time": "2025-05-09T07:10:48.426742Z", "start_time": "2025-05-09T07:10:48.124257Z"}}, "source": ["\n", "data = pd.read_csv(f'./spikedata2.Umax=2400/Ca_cell_spike_ref_220_Umax=2400.csv', delimiter=',')\n", "data['spike_time'] = data['spike_time'].str.replace(' ms', '').astype('float')\n", "data = data.values\n", "\n", "data1 = pd.read_csv(f'./spikedata2.Umax=2400/Ca_cell_spike_ref_440_Umax=2400.csv', delimiter=',')\n", "data1['spike_time'] = data1['spike_time'].str.replace(' ms', '').astype('float')\n", "data1 = data1.values \n", "\n", "# === Intervalos de estado estacionário ===\n", "# 20% MVC\n", "t_start_20, t_end_20 = 4500, 54500  # ms\n", "window_20 = (t_end_20 - t_start_20) / 1000  # s\n", "steady_data_20 = data[(data[:, 1] >= t_start_20) & (data[:, 1] <= t_end_20)]\n", "\n", "# 40% MVC\n", "t_start_40, t_end_40 = 4500, 54500  # ms \n", "window_40 = (t_end_40 - t_start_40) / 1000  # s\n", "steady_data_40 = data1[(data1[:, 1] >= t_start_40) & (data1[:, 1] <= t_end_40)]\n", "\n", "# === Seleção de neurônios ===\n", "unique_neurons = np.unique(data[:, 0])\n", "unique_neurons1= np.unique(data1[:, 0])\n", "neurons_20 = np.random.choice(unique_neurons, size=11, replace=True)\n", "print(neurons_20)\n", "neurons_40 = np.random.choice(unique_neurons1, size=9, replace=True)\n", "print(neurons_40)\n", "\n", "# === Calcular taxa de disparo (spikes/s) ===\n", "# 20%\n", "fr_20 = []\n", "for neuron in neurons_20:\n", "    n_spikes = np.sum(steady_data_20[:, 0] == neuron)\n", "    fr = n_spikes / window_20\n", "    fr_20.append(fr)\n", "\n", "# 40%\n", "fr_40 = []\n", "for neuron in neurons_40:\n", "    n_spikes = np.sum(steady_data_40[:, 0] == neuron)\n", "    fr = n_spikes / window_40\n", "    fr_40.append(fr)\n", "\n", "# === Plot ===\n", "plt.figure(figsize=(6, 5))\n", "\n", "# Posições no eixo X\n", "x_20 = 0\n", "x_40 = 0.7\n", "\n", "# Jitter\n", "jitter_20 = np.random.uniform(-0.05, 0.05, size=len(fr_20))\n", "jitter_40 = np.random.uniform(-0.05, 0.05, size=len(fr_40))\n", "\n", "# Pontos\n", "plt.scatter(np.full(len(fr_20), x_20) + jitter_20, fr_20, color='royalblue', alpha=0.8, s=60, label='20% MVC')\n", "plt.scatter(np.full(len(fr_40), x_40) + jitter_40, fr_40, color='tomato', alpha=0.8, s=60, label='40% MVC')\n", "\n", "# Médias e barras de erro\n", "mean_20 = np.mean(fr_20)\n", "sem_20 = np.std(fr_20) / np.sqrt(len(fr_20))\n", "plt.errorbar(x_20, mean_20, yerr=sem_20, fmt='_', color='black', capsize=5, markersize=14, elinewidth=1.5)\n", "\n", "mean_40 = np.mean(fr_40)\n", "sem_40 = np.std(fr_40) / np.sqrt(len(fr_40))\n", "plt.errorbar(x_40, mean_40, yerr=sem_40, fmt='_', color='black', capsize=5, markersize=14, elinewidth=1.5)\n", "\n", "# Customizações\n", "plt.xticks([x_20, x_40], ['20% MVC - Controle', '40% MVC - Controle'])\n", "plt.ylabel('Taxa de descarga (pps)')\n", "plt.title('Taxa de descarga na fase de estado estacionário')\n", "plt.xlim(-0.3, 1.0)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[29. 52. 35. 33. 71. 44. 83.  0. 55. 35. 21.]\n", "[40. 75. 13. 39. 94. 83. 74. 52. 93.]\n"]}, {"data": {"text/plain": ["<Figure size 600x500 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 8}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:10:29.044105Z", "start_time": "2025-05-10T07:10:29.039710Z"}}, "source": ["import os\n", "import shutil\n", "import sys\n", "import platform\n", "sys.path.append('./../')\n", "\n", "\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pyNN\n", "import pyNN.neuron as sim\n", "import pyNN.space as space\n", "import pandas as pd\n", "\n", "\n", "from neuroml import Morphology, Segment, Point3DWithDiam as P\n", "from neuron import h, hclass\n", "from pyNN import neuron\n", "from pyNN.models import BaseCellType\n", "from pyNN.morphology import NeuroMLMorphology, NeuriteDistribution, Morphology as Morph, IonChannelDistribution\n", "from pyNN.neuron import NativeCellType\n", "from pyNN.neuron.cells import RandomSpikeSource, _new_property\n", "from pyNN.neuron.morphology import uniform, random_section, random_placement, at_distances, apical_dendrites, dendrites, centre\n", "from pyNN.neuron.simulator import state\n", "from pyNN.parameters import IonicSpecies\n", "from pyNN.random import RandomDistribution, NumpyRNG\n", "from pyNN.space import Grid2D, RandomStructure, Sphere\n", "from pyNN.standardmodels import StandardIonChannelModel, build_translations, StandardCellType, StandardModelType\n", "from pyNN.standardmodels.cells import SpikeSourceGamma, MultiCompartmentNeuron as mc\n", "from pyNN.utility.build import compile_nmodl\n", "from pyNN.utility.plotting import Figure, Panel\n", "import src.Classes as Classes\n", "import src.functions as funçoes\n", "from src.functions import neuromuscular_system, soma_força\n", "\n", "import platform\n"], "outputs": [], "execution_count": 3}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:10:29.401614Z", "start_time": "2025-05-10T07:10:29.055860Z"}}, "source": ["\n", "files = os.listdir('../src/')\n", "\n", "if platform.system() == 'Linux':\n", "    for filename in files:\n", "        if filename.endswith('.mod'):\n", "            shutil.copyfile(f'./../src/{filename}',f'./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/{filename}')\n", "\n", "    compile_nmodl('./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl')\n", "    h.nrn_load_dll('./../.venv/lib/python3.10/site-packages/pyNN/neuron/nmodl/mn.o')\n", "\n", "if platform.system() == 'Windows':\n", "    for filename in files:\n", "        if filename.endswith('.mod'):\n", "            shutil.copyfile(f'./../src/{filename}',f'../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/{filename}')\n", "\n", "    compile_nmodl('../modelpynn/Lib/site-packages/pyNN/neuron/nmodl')\n", "    h.nrn_load_dll('../modelpynn/Lib/site-packages/pyNN/neuron/nmodl/mn.o')"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nrnivmodl found at /home/<USER>/code/Modelo-Motoneuronio/.venv/bin/nrnivmodl\n", "Successfully compiled NEURON extensions.\n"]}], "execution_count": 4}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-10T07:10:29.464821Z", "start_time": "2025-05-10T07:10:29.412211Z"}}, "source": ["timestep = 0.05\n", "sim.setup(timestep=timestep)\n", "Tf = 30000\n", "\n", "n = 100\n", "somas = funçoes.create_somas(n)\n", "dends = funçoes.create_dends(n,somas)\n", "\n", "cell_type = Classes.cell_class(\n", "    morphology= funçoes.soma_dend(somas, dends) ,\n", "    cm=1,    # mF / cm**2\n", "    Ra=0.070, # ohm.mm\n", "    ionic_species={\"na\": IonicSpecies(\"na\", reversal_potential=50),\n", "                   \"ks\": IonicSpecies(\"ks\", reversal_potential=-80),\n", "                   \"kf\": IonicSpecies(\"kf\", reversal_potential=-80)\n", "                  },\n", "    pas_soma = {\"conductance_density\": uniform('soma', 7e-4), \"e_rev\":-70},\n", "    pas_dend = {\"conductance_density\": uniform('dendrite', 7e-4), \"e_rev\":-70},\n", "    na = {\"conductance_density\": uniform('soma', 10), \"vt\":list(np.linspace(-57.65, -53,n))},\n", "    kf = {\"conductance_density\": uniform('soma', 1), \"vt\":list(np.linspace(-57.65, -53, n))},\n", "    ks = {\"conductance_density\": uniform('soma', 0.5), \"vt\":list(np.linspace(-57.65, -53, n))},\n", "    syn={\"locations\": centre('dendrite'),\n", "         \"e_syn\": 0,\n", "         \"tau_syn\": 0.6},  \n", ")\n", "\n", "cells = sim.Population(n, cell_type, initial_values={'v': list(-70*np.ones(n))})\n", "\n", "muscle_units, force_objects, neuromuscular_junctions = neuromuscular_system(cells, n, h, Umax=1600)\n", "np.random.seed(237371)\n", "spike_source = sim.Population(400, Classes.SpikeSourceGammaStart(alpha=1)) \n", "                                                        #start=RandomDistribution('uniform', [0, 3.0], rng=NumpyRNG(seed=4242))))\n", "syn = sim.StaticSynapse(weight=0.6, delay=0.2)\n", "# nmj = sim.StaticSynapse(weight=1, delay=0.2)\n", "input_conns = sim.Projection(spike_source, cells, \n", "                             sim.FixedProbabilityConnector(0.3, location_selector='dendrite'), \n", "                             syn, receptor_type=\"syn\")\n", "spike_source.record('spikes')\n", "cells.record('spikes')\n", "cells[0:2].record('v', locations=('dendrite','soma'))\n", "cells[0:2].record(('na.m', 'na.h'), locations='soma')\n", "cells[0:2].record(('kf.n'), locations='soma')\n", "cells[0:2].record(('ks.p'), locations='soma')\n", "f = dict()\n", "for i in range(n):\n", "    f[i] = h.Vector().record(force_objects[i]._ref_F)\n", "\n", "\n"], "outputs": [{"ename": "AttributeError", "evalue": "'hoc.HocObject' object has no attribute 'muscle_unit_calcium'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 29\u001b[0m\n\u001b[1;32m      9\u001b[0m cell_type \u001b[38;5;241m=\u001b[39m Classes\u001b[38;5;241m.\u001b[39mcell_class(\n\u001b[1;32m     10\u001b[0m     morphology\u001b[38;5;241m=\u001b[39m funçoes\u001b[38;5;241m.\u001b[39msoma_dend(somas, dends) ,\n\u001b[1;32m     11\u001b[0m     cm\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m,    \u001b[38;5;66;03m# mF / cm**2\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     24\u001b[0m          \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtau_syn\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;241m0.6\u001b[39m},  \n\u001b[1;32m     25\u001b[0m )\n\u001b[1;32m     27\u001b[0m cells \u001b[38;5;241m=\u001b[39m sim\u001b[38;5;241m.\u001b[39mPopulation(n, cell_type, initial_values\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mv\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m70\u001b[39m\u001b[38;5;241m*\u001b[39mnp\u001b[38;5;241m.\u001b[39mones(n))})\n\u001b[0;32m---> 29\u001b[0m muscle_units, force_objects, neuromuscular_junctions \u001b[38;5;241m=\u001b[39m \u001b[43mneuromuscular_system\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcells\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mUmax\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1600\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     30\u001b[0m np\u001b[38;5;241m.\u001b[39mrandom\u001b[38;5;241m.\u001b[39mseed(\u001b[38;5;241m237371\u001b[39m)\n\u001b[1;32m     31\u001b[0m spike_source \u001b[38;5;241m=\u001b[39m sim\u001b[38;5;241m.\u001b[39mPopulation(\u001b[38;5;241m400\u001b[39m, Classes\u001b[38;5;241m.\u001b[39mSpikeSourceGammaStart(alpha\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)) \n", "File \u001b[0;32m~/code/Modelo-Motoneuronio/src/functions.py:286\u001b[0m, in \u001b[0;36mneuromuscular_system\u001b[0;34m(cells, n, h, Umax)\u001b[0m\n\u001b[1;32m    284\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(n):\n\u001b[1;32m    285\u001b[0m     muscle_units[i] \u001b[38;5;241m=\u001b[39m h\u001b[38;5;241m.\u001b[39mSection(name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmu\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 286\u001b[0m     force_objects[i] \u001b[38;5;241m=\u001b[39m \u001b[43mh\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmuscle_unit_calcium\u001b[49m(muscle_units[i](\u001b[38;5;241m0.5\u001b[39m))\n\u001b[1;32m    287\u001b[0m     neuromuscular_junctions[i] \u001b[38;5;241m=\u001b[39m h\u001b[38;5;241m.\u001b[39mNetCon(\n\u001b[1;32m    288\u001b[0m         cells\u001b[38;5;241m.\u001b[39mall_cells[i]\u001b[38;5;241m.\u001b[39m_cell\u001b[38;5;241m.\u001b[39msections[\u001b[38;5;241m0\u001b[39m](\u001b[38;5;241m0.5\u001b[39m)\u001b[38;5;241m.\u001b[39m_ref_v,\n\u001b[1;32m    289\u001b[0m         force_objects[i],\n\u001b[1;32m    290\u001b[0m         sec\u001b[38;5;241m=\u001b[39mcells\u001b[38;5;241m.\u001b[39mall_cells[i]\u001b[38;5;241m.\u001b[39m_cell\u001b[38;5;241m.\u001b[39msections[\u001b[38;5;241m0\u001b[39m],\n\u001b[1;32m    291\u001b[0m     )\n\u001b[1;32m    293\u001b[0m     force_objects[i]\u001b[38;5;241m.\u001b[39mFmax \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.03\u001b[39m \u001b[38;5;241m+\u001b[39m (\u001b[38;5;241m3\u001b[39m \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m0.03\u001b[39m) \u001b[38;5;241m*\u001b[39m i \u001b[38;5;241m/\u001b[39m n\n", "\u001b[0;31mAttributeError\u001b[0m: 'hoc.HocObject' object has no attribute 'muscle_unit_calcium'"]}], "execution_count": 5}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["valor: 55\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m n \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m ref \u001b[38;5;129;01min\u001b[39;00m refs:   \n\u001b[1;32m----> 5\u001b[0m     \u001b[43msim\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mTf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43mClasses\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mSetRateIntControl\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspike_source\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcells\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mforce_objects\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mref\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfim simulação\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      7\u001b[0m     \u001b[38;5;66;03m# print('t=4000')\u001b[39;00m\n\u001b[0;32m      8\u001b[0m     \u001b[38;5;66;03m# sim.run(Tf, callbacks=[Classes.SetRate(spike_source, cells, force_objects, ref=ref)])\u001b[39;00m\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;66;03m#teste força\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\Modelo-Motoneuronio\\modelpynn\\lib\\site-packages\\pyNN\\common\\control.py:114\u001b[0m, in \u001b[0;36mbuild_run.<locals>.run\u001b[1;34m(simtime, callbacks)\u001b[0m\n\u001b[0;32m     99\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrun\u001b[39m(simtime, callbacks\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    100\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    101\u001b[0m \u001b[38;5;124;03m    Advance the simulation by `simtime` ms.\u001b[39;00m\n\u001b[0;32m    102\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    112\u001b[0m \u001b[38;5;124;03m    the initial conditions (time ``t = 0``), use the ``reset()`` function.\u001b[39;00m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 114\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrun_until\u001b[49m\u001b[43m(\u001b[49m\u001b[43msimulator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43msimtime\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\Modelo-Motoneuronio\\modelpynn\\lib\\site-packages\\pyNN\\common\\control.py:92\u001b[0m, in \u001b[0;36mbuild_run.<locals>.run_until\u001b[1;34m(time_point, callbacks)\u001b[0m\n\u001b[0;32m     89\u001b[0m             active_callbacks\u001b[38;5;241m.\u001b[39mappend(callback_events\u001b[38;5;241m.\u001b[39mpop()[\u001b[38;5;241m1\u001b[39m])\n\u001b[0;32m     91\u001b[0m         \u001b[38;5;28mnext\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmin\u001b[39m(\u001b[38;5;28mnext\u001b[39m, time_point)\n\u001b[1;32m---> 92\u001b[0m         \u001b[43msimulator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstate\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_until\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     93\u001b[0m         callback_events\u001b[38;5;241m.\u001b[39mextend((callback(simulator\u001b[38;5;241m.\u001b[39mstate\u001b[38;5;241m.\u001b[39mt), callback)\n\u001b[0;32m     94\u001b[0m                                \u001b[38;5;28;01mfor\u001b[39;00m callback \u001b[38;5;129;01min\u001b[39;00m active_callbacks)\n\u001b[0;32m     95\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "\n", "refs = [55, 110, 220, 330, 440, 550, 660, 770, 880, 990, 1100]  \n", "n = 0\n", "for ref in refs:   \n", "\n", "    sim.run(Tf, callbacks=[Classes.SetRateIntControl(spike_source, cells, force_objects, ref=ref)])\n", "    print('fim simulação')\n", "    # print('t=4000')\n", "    # sim.run(Tf, callbacks=[Classes.SetRate(spike_source, cells, force_objects, ref=ref)])\n", "    #teste força\n", "    força_total = soma_força(force_objects, h, f)\n", "    rate = 10+((ref - força_total) * 0.01 + 0.1*np.cumsum(força_total)*0.05)\n", "    t = np.arange(0, Tf, timestep)\n", "    plt.plot(t, força_total)\n", "    plt.show()\n", "\n", "    new_folder = \"força2.Umax=1600\"\n", "    os.makedirs(new_folder, exist_ok=True)\n", "    df = pd.DataFrame({'time': t, 'força_total': força_total, 'rate': rate})\n", "    filename = os.path.join(new_folder, f'Ca_forca_ref{ref}_Umax=1600.csv')\n", "    df.to_csv(filename, index=False) \n", "\n", "    data_source = spike_source.get_data().segments[n]\n", "    data = cells.get_data().segments[n]\n", "\n", "    #teste spike_datasource\n", "    spike_df = pd.DataFrame([{\"neuron_id\": neuron_id, \"spike_time\": spike_time}\n", "        for neuron_id, spikes in enumerate(data_source.spiketrains)\n", "        for spike_time in spikes])\n", "\n", "    new_folder1 = \"spikedatasource2.Umax=1600\"\n", "    os.makedirs(new_folder1, exist_ok=True)    \n", "    filename = os.path.join(new_folder1, f'Ca_spike_data_ref_{ref}_Umax=1600.csv')\n", "    spike_df.to_csv(filename, index=False)\n", "\n", "    plt.scatter(spike_df[\"spike_time\"], spike_df[\"neuron_id\"], s=4, label=f\"ref={ref}\")\n", "\n", "    plt.show()\n", "\n", "    #teste spike_data\n", "    cell_spike_df = pd.DataFrame([{\"neuron_id\": neuron_id, \"spike_time\": spike_time}\n", "        for neuron_id, spikes in enumerate(data.spiketrains)\n", "        for spike_time in spikes])\n", "\n", "    new_folder2 = \"spikedata2.Umax=1600\"\n", "    os.makedirs(new_folder2, exist_ok=True)    \n", "    filename = os.path.join(new_folder2, f'Ca_cell_spike_ref_{ref}_Umax=1600.csv')\n", "    cell_spike_df.to_csv(filename, index=False)\n", "\n", "    plt.scatter(cell_spike_df[\"spike_time\"], cell_spike_df[\"neuron_id\"], s=4,label=f\"ref={ref}\")\n", "\n", "    plt.show()\n", "    data = cells.get_data().segments[0]\n", "    vm = data.filter(name=\"soma.v\")[0]\n", "    Figure(\n", "    Panel(vm, ylabel=\"Membrane potential (mV)\", xticks=True, yticks=True),\n", ")\n", "    plt.show()\n", "    sim.reset()\n", "    for i in range(100):\n", "        force_objects[i].F = 0\n", "        force_objects[i].x1 = 0\n", "        force_objects[i].x2 = 0\n", "    n = n + 1\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}